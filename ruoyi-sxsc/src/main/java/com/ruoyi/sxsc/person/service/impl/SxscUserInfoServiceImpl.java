package com.ruoyi.sxsc.person.service.impl;

import com.aliyun.cloudauth20190307.models.Id2MetaVerifyResponseBody;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityOrder;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeService;
import com.ruoyi.sxsc.person.domain.SxscUserCommission;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.mapper.SxscUserInfoMapper;
import com.ruoyi.sxsc.person.model.*;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.sxsc.utils.IdCardUtil;
import com.ruoyi.sxsc.utils.SMSUtils;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 人员基本信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
public class SxscUserInfoServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserInfoMapper,SxscUserInfo> implements ISxscUserInfoService
{

    @Autowired
    ISysUserService iSysUserService;

    @Autowired
    IdCardUtil idCardUtil;

    @Autowired
    SMSUtils smsUtils;

    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    ISxscUserCommissionService iSxscUserCommissionService;

    @Autowired
    private ISysConfigService configService;


    /**
     * 查询人员基本信息
     * 
     * @param userId 人员基本信息主键
     * @return 人员基本信息
     */
    @Override
    public SxscUserInfo selectSxscUserInfoByUserId(Long userId)
    {
        SxscUserInfo sxscUserInfo=getById(userId);
        if(StringUtils.isNotNull(sxscUserInfo)){
            sxscUserInfo.setSysUser(iSysUserService.selectUserMainById(userId));
            sxscUserInfo.setInviterSysUser(iSysUserService.selectUserMainById(sxscUserInfo.getParentId()));
            sxscUserInfo.setNodeSysUser(iSysUserService.selectUserMainById(sxscUserInfo.getNodeId()));
        }
        return sxscUserInfo;
    }

    /**
     * 查询人员基本信息列表
     * 
     * @param sxscUserInfo 人员基本信息
     * @return 人员基本信息
     */
    @Override
    public List<SxscUserInfo> selectSxscUserInfoList(SxscUserInfoModel sxscUserInfo)
    {
        LambdaQueryWrapper<SxscUserInfo> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscUserInfo.getIdentityName()),SxscUserInfo::getIdentityName,sxscUserInfo.getIdentityName());

        wrapper.eq(StringUtils.isNotNull(sxscUserInfo.getIdentitySex()),SxscUserInfo::getIdentitySex,sxscUserInfo.getIdentitySex());

        wrapper.apply(StringUtils.isNotNull(sxscUserInfo.getPhonenumber())," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserInfo.getPhonenumber()+"', '%'))");

        wrapper.like(StringUtils.isNotNull(sxscUserInfo.getIdentityNumber()),SxscUserInfo::getIdentityNumber,sxscUserInfo.getIdentityNumber());

        wrapper.like(StringUtils.isNotNull(sxscUserInfo.getAliPayName()),SxscUserInfo::getAliPayName,sxscUserInfo.getAliPayName());

        wrapper.like(StringUtils.isNotNull(sxscUserInfo.getAliPayAcc()),SxscUserInfo::getAliPayAcc,sxscUserInfo.getAliPayAcc());

        wrapper.like(StringUtils.isNotNull(sxscUserInfo.getEffective()),SxscUserInfo::getEffective,sxscUserInfo.getEffective());

        wrapper.like(StringUtils.isNotNull(sxscUserInfo.getNodeAcc()),SxscUserInfo::getNodeAcc,sxscUserInfo.getNodeAcc());

        wrapper.eq(StringUtils.isNotNull(sxscUserInfo.getCopartner()),SxscUserInfo::getCopartner,sxscUserInfo.getCopartner());

        wrapper.orderByDesc(SxscUserInfo::getUserId);

        wrapper.eq(SxscUserInfo::getDelFlag,0l);

        List<SxscUserInfo> userInfos=list(wrapper);

        for(SxscUserInfo userInfo:userInfos ){
            userInfo.setSysUser(iSysUserService.selectUserMainById(userInfo.getUserId()));
        }
        return userInfos;
    }

    /**
     * 新增人员基本信息
     * 
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    @Override
    public int insertSxscUserInfo(SxscUserInfo sxscUserInfo)
    {
        sxscUserInfo.setBlindBox(new BigDecimal(configService.selectConfigByKey("user.blind.box.initial")));
        sxscUserInfo.setCreateBy(SecurityUtils.getUsername());
        sxscUserInfo.setCreateTime(DateUtils.getNowDate());
        return save(sxscUserInfo)?1:0;
    }

    /**
     * 修改人员基本信息
     * 
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserInfo(SxscUserInfo sxscUserInfo)
    {
        sxscUserInfo.setUpdateBy(SecurityUtils.getUsername());
        sxscUserInfo.setUpdateTime(DateUtils.getNowDate());
        updateById(sxscUserInfo);
        return AjaxResult.success();
    }



    /**
     * 修改人员基本信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    public AjaxResult updateSxscUserInfo(SxscUserInfoModel sxscUserInfo){
        Long userId=SecurityUtils.getUserId();
        if(SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            userId=sxscUserInfo.getUserId();
        }
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getAliPayAcc()),SxscUserInfo::getAliPayAcc,sxscUserInfo.getAliPayAcc());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getAliPayName()),SxscUserInfo::getAliPayName,sxscUserInfo.getAliPayName());

        if(StringUtils.isNotNull(sxscUserInfo.getIdentityNumber())){

            LambdaQueryWrapper<SxscUserInfo> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.eq(SxscUserInfo::getIdentityNumber,sxscUserInfo.getIdentityNumber());
            queryWrapper.eq(SxscUserInfo::getDelFlag,0);
            if(count(queryWrapper)>0){
                return  AjaxResult.error("实名信息已被绑定！");
            }

            SxscUserInfo userInfo=getById(userId);

            if(StringUtils.isNotNull(userInfo.getIdentityTime())&&userId.equals(SecurityUtils.getUserId())){
                return AjaxResult.error("已通过实名认证，无需再次认证");
            }

            Id2MetaVerifyResponseBody responseBody = idCardUtil.identifyAuthentication(sxscUserInfo.getIdentityName(),sxscUserInfo.getIdentityNumber());
            if(responseBody.getCode().equals("401")){
                return  AjaxResult.error("身份证号与姓名不符！");
            }
            if(responseBody.getCode().equals("400")){
                return  AjaxResult.error("身份证号或姓名不能为空！");
            }
            if(!responseBody.getResultObject().getBizCode().equals("1")){
                return  AjaxResult.error("身份证号与姓名不符！");
            }
            updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIdentityName()),SxscUserInfo::getIdentityName,sxscUserInfo.getIdentityName());
            updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIdentitySex()),SxscUserInfo::getIdentitySex,sxscUserInfo.getIdentitySex());
            updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIdentityDate()),SxscUserInfo::getIdentityDate,sxscUserInfo.getIdentityDate());
            updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIdentityNumber()),SxscUserInfo::getIdentityNumber,sxscUserInfo.getIdentityNumber());
            updateWrapper.set(SxscUserInfo::getIdentityTime,DateUtils.getNowDate());
            //iSxscBillIntegralService.insertSxscBillIntegral(IdUtils.fastSimpleUUID(),"新用户注册奖励",5L,new BigDecimal("30"),userId);
        }

        if(StringUtils.isNotEmpty(sxscUserInfo.getInvitationCode())){
            SxscUserInfo userInfo=getById(userId);
            if(StringUtils.isNotNull(userInfo.getParentId())){
                return AjaxResult.error("已存在邀请人，无法修改");
            }
            SysUser sysUser=iSysUserService.selectUserByUserName(sxscUserInfo.getInvitationCode());
            if(StringUtils.isNull(sysUser)||sysUser.getUserId().equals(SecurityUtils.getUserId())){
                return AjaxResult.error("邀请人不存在");
            }
            updateWrapper.set(SxscUserInfo::getParentTime,DateUtils.getNowDate());
            updateWrapper.set(SxscUserInfo::getParentId,sysUser.getUserId());
        }
        updateWrapper.set(SxscUserInfo::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscUserInfo::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.eq(SxscUserInfo::getUserId,userId);
        update(updateWrapper);
        SysUser sysUser=new SysUser();
        sysUser.setUserId(userId);
        sysUser.setAvatar(sxscUserInfo.getAvatar());
        sysUser.setNickName(sxscUserInfo.getNickName());
        sysUser.setEmail(sxscUserInfo.getEmail());
        sysUser.setSex(sxscUserInfo.getSex());
        if(StringUtils.isNotNull(sxscUserInfo.getPassword())){
            // 校验验证码
            SysUserMain sysUserData=iSysUserService.selectUserMainById(userId);
            smsUtils.verifySmsCode(sysUserData.getPhonenumber(), sxscUserInfo.getCode());
            sysUser.setPassword(SecurityUtils.encryptPassword(sxscUserInfo.getPassword()));
        }

        iSysUserService.updateUserProfile(sysUser);

        return AjaxResult.success();
    }

    /**
     * 修改人员节点信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserInfoNodeId(SxscUserInfoModel sxscUserInfo){

        SxscUserInfo userInfo=getById(SecurityUtils.getUserId());
        if(StringUtils.isNotNull(userInfo.getNodeId())){
            return AjaxResult.error("已存在节点人，无法修改");
        }
        SysUser sysUser=iSysUserService.selectUserByUserName(sxscUserInfo.getNodeCode());
        if(StringUtils.isNull(sysUser)||sysUser.getUserId().equals(SecurityUtils.getUserId())){
            return AjaxResult.error("节点人不存在");
        }
        LambdaQueryWrapper<SxscUserInfo> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(SxscUserInfo::getNodeId,sysUser.getUserId());
        wrapper.eq(SxscUserInfo::getDelFlag,0L);
        int nodeCount=count(wrapper);
        if(nodeCount>=2){
            return AjaxResult.error("节点下关系已满");
        }
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserInfo::getNodeTime,DateUtils.getNowDate());
        updateWrapper.set(SxscUserInfo::getNodeId,sysUser.getUserId());
        updateWrapper.eq(SxscUserInfo::getUserId,userInfo.getUserId());
        update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 修改人员基本信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserInfoTicketAcc(SxscUserInfoModel sxscUserInfo){
        Long userId=SecurityUtils.getUserId();
        if(SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            userId=sxscUserInfo.getUserId();
        }
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserInfo::getTicketAcc,sxscUserInfo.getTicketAcc());
        updateWrapper.set(SxscUserInfo::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscUserInfo::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.eq(SxscUserInfo::getUserId,userId);
        update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 修改人员基本信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserInfoPtAddress(SxscUserInfoModel sxscUserInfo){
        Long userId=SecurityUtils.getUserId();
        if(SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            userId=sxscUserInfo.getUserId();
        }
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserInfo::getPtAddress,sxscUserInfo.getPtAddress());
        updateWrapper.set(SxscUserInfo::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscUserInfo::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.eq(SxscUserInfo::getUserId,userId);
        update(updateWrapper);
        return AjaxResult.success();
    }

    /**
     * 修改人员基本信息
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserRelease(SxscUserInfoModel sxscUserInfo){
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserInfo::getIntegralReleaseRatio,sxscUserInfo.getIntegralReleaseRatio());
        updateWrapper.set(SxscUserInfo::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscUserInfo::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.eq(SxscUserInfo::getUserId,sxscUserInfo.getUserId());
        update(updateWrapper);
        return AjaxResult.success();
    }
    /**
     * 修改人员积分
     *
     * @param sxscUserInfo 人员基本信息
     * @return 结果
     */
    public void updateSxscUserInfoIntegral(SxscUserInfo sxscUserInfo){
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getShare()),SxscUserInfo::getShare,sxscUserInfo.getShare());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIntegralGxz()),SxscUserInfo::getIntegralGxz,sxscUserInfo.getIntegralGxz());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIntegralTy()),SxscUserInfo::getIntegralTy,sxscUserInfo.getIntegralTy());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIntegralJl()),SxscUserInfo::getIntegralJl,sxscUserInfo.getIntegralJl());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIntegralSj()),SxscUserInfo::getIntegralSj,sxscUserInfo.getIntegralSj());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getIntegralSzqz()),SxscUserInfo::getIntegralSzqz,sxscUserInfo.getIntegralSzqz());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getBlindBox()),SxscUserInfo::getBlindBox,sxscUserInfo.getBlindBox());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getAcceptorAmount()),SxscUserInfo::getAcceptorAmount,sxscUserInfo.getAcceptorAmount());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getUpdateTime()),SxscUserInfo::getUpdateTime,sxscUserInfo.getUpdateTime());
        updateWrapper.set(StringUtils.isNotNull(sxscUserInfo.getUpdateBy()),SxscUserInfo::getUpdateBy,sxscUserInfo.getUpdateBy());
        updateWrapper.eq(SxscUserInfo::getUserId,sxscUserInfo.getUserId());
        update(updateWrapper);
    }

    /**
     * 换绑当前账号
     *
     * @param changeBindingModel
     * @return 结果
     */
    @Override
    public AjaxResult changeBinding(SxscUserInfoChangeBindingModel changeBindingModel){
        // 校验验证码
        smsUtils.verifySmsCode(changeBindingModel.getOldPhone(), changeBindingModel.getOldCode());

        // 校验验证码
        smsUtils.verifySmsCode(changeBindingModel.getNewPhone(), changeBindingModel.getNewCode());

        SysUser sysUser=new SysUser();
        sysUser.setUserId(SecurityUtils.getUserId());
        sysUser.setUserName(changeBindingModel.getNewPhone());
        sysUser.setPhonenumber(changeBindingModel.getNewPhone());
        iSysUserService.updateUserStatus(sysUser);

        return AjaxResult.success();
    }

    /**
     * 批量删除人员基本信息
     *
     * @param userIds 需要删除的人员基本信息主键
     * @return 结果
     */
    @Override
    public AjaxResult deleteSxscUserInfoByUserIds(Long[] userIds)
    {
        LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(SxscUserInfo::getDelFlag,1l);
        updateWrapper.set(SxscUserInfo::getUpdateTime,DateUtils.getNowDate());
        updateWrapper.set(SxscUserInfo::getUpdateBy,SecurityUtils.getUsername());
        updateWrapper.in(SxscUserInfo::getUserId,userIds);
        update(updateWrapper);
        iSysUserService.deleteUserByIds(userIds);
        return AjaxResult.success();
    }





    /**
     * 查询当前登陆人推广人订单
     *
     * @param sxscUserInfo 筛选条件
     * @return 结果
     */
    public List<SxscUserInfoExtensionOrderModelRes> extensionOrderList(SxscUserInfoExtensionModelReq sxscUserInfo){

        LambdaQueryWrapper<SxscUserCommission> wrapper=new LambdaQueryWrapper();
        wrapper.eq(SxscUserCommission::getUserId,SecurityUtils.getUserId());
        wrapper.orderByDesc(SxscUserCommission::getCreateTime);
        if(StringUtils.isNotEmpty(sxscUserInfo.getStartDate())){
            wrapper.apply("'"+sxscUserInfo.getStartDate()+"' <= DATE_FORMAT(create_time,'%Y-%m-%d') " );
        }
        if(StringUtils.isNotEmpty(sxscUserInfo.getEndDate())){
            wrapper.apply(" DATE_FORMAT(create_time,'%Y-%m-%d') <= '" +sxscUserInfo.getEndDate()+"'");
        }
        List<SxscUserCommission> userCommissionList=iSxscUserCommissionService.list(wrapper);

        List<SxscUserInfoExtensionOrderModelRes> list=new ArrayList<>();

        for(SxscUserCommission sxscUserCommission:userCommissionList){
            SxscCommodityOrder order=iSxscCommodityOrderService.selectSxscCommodityOrderById(sxscUserCommission.getCommodityOrderId());
            if(StringUtils.isNotNull(order)){
                SxscUserInfoExtensionOrderModelRes orderModelRes=new SxscUserInfoExtensionOrderModelRes();
                orderModelRes.setOrder(order);
                orderModelRes.setSysUser(iSysUserService.selectUserMainById(order.getBuyerUserId()));
                orderModelRes.setToCommission((order.getStatus()==4||order.getStatus()==5)?1l:0);
                orderModelRes.setCommission(sxscUserCommission.getCommission());
                list.add(orderModelRes);
            }
        }

        return list;
    }


    /**
     * 导入用户数据
     *
     * @param infoModelExcels 数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importInfo(List<SxscUserInfoModelExcel> infoModelExcels, Boolean isUpdateSupport, String operName){
        if (StringUtils.isNull(infoModelExcels) || infoModelExcels.size() == 0)
        {
            throw new ServiceException("导入用户数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        String password = configService.selectConfigByKey("sys.user.initPassword");
        for (SxscUserInfoModelExcel infoModelExcel : infoModelExcels)
        {
            try
            {

                SysUser sysUser = new SysUser();
                sysUser.setUserName(infoModelExcel.getPhonenumber());
                sysUser.setNickName(infoModelExcel.getNickName());
                sysUser.setSex(infoModelExcel.getSex());
                sysUser.setPassword(SecurityUtils.encryptPassword(password));
                sysUser.setPhonenumber(infoModelExcel.getPhonenumber());
                //初始化会员角色
                sysUser.setRoleIds(new Long[]{101l});
                sysUser.setDeptId(101l);
                sysUser.setUserType("01");
                iSysUserService.insertUser(sysUser);

                SxscUserInfo sxscUserInfo=new SxscUserInfo();
                sxscUserInfo.setUserId(sysUser.getUserId());
                sxscUserInfo.setAliPayAcc(infoModelExcel.getAliPayAcc());
                sxscUserInfo.setAliPayName(infoModelExcel.getAliPayName());
                sxscUserInfo.setIdentityDate(infoModelExcel.getIdentityDate());
                sxscUserInfo.setIdentityName(infoModelExcel.getIdentityName());
                sxscUserInfo.setIdentityNumber(infoModelExcel.getIdentityNumber());
                sxscUserInfo.setIdentitySex(infoModelExcel.getIdentitySex());
                sxscUserInfo.setIdentityTime(infoModelExcel.getIdentityTime());
                this.insertSxscUserInfo(sxscUserInfo);
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、手机号《 " + infoModelExcel.getPhonenumber() + " 》导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
