package com.ruoyi.sxsc.person.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 人员基本信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInfoModel
{

    /** 主键 */
    private Long userId;

    /** 身份证姓名 */
    @Excel(name = "身份证姓名")
    private String identityName;

    /** 身份证性别 */
    @Excel(name = "身份证性别")
    private String identitySex;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String identityNumber;

    /** 身份证有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "身份证有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date identityDate;

    /** 支付宝姓名 */
    @Excel(name = "支付宝姓名")
    private String aliPayName;

    /** 支付宝账号 */
    @Excel(name = "支付宝账号")
    private String aliPayAcc;

    /** 用户昵称 */
    private String nickName;

    /** 用户手机号 */
    private String phonenumber;

    /** 用户性别 */
    private String sex;

    /** 用户头像 */
    private String avatar;

    /** 用户邮箱 */
    private String email;

    /** 验证码 */
    private String code;

    /** 密码 */
    private String password;

    /** 邀请码 */
    private String invitationCode;

    /** 有效用户 */
    private Long effective;

    /** 承兑商 */
    private Long nodeAcc;

    /** 票证账号 */
    private String ticketAcc;

    /** 节点码 */
    private String nodeCode;

    /** 合伙人身份1初级2中级3高级4战略 */
    private Long copartner;

    /** PT地址 */
    private String ptAddress;

    /** 释放比例 */
    private BigDecimal integralReleaseRatio;
}
