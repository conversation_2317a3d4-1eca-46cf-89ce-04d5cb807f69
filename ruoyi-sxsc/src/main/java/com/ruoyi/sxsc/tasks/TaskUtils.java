package com.ruoyi.sxsc.tasks;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.constant.IntegralBillConstants;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.bill.domain.SxscBillSystemIntegralGive;
import com.ruoyi.sxsc.bill.service.ISxscBillIntegralService;
import com.ruoyi.sxsc.bill.service.ISxscBillSystemIntegralGiveService;
import com.ruoyi.sxsc.commodity.domain.*;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityOrderMapper;
import com.ruoyi.sxsc.commodity.service.*;
import com.ruoyi.sxsc.consume.domain.SxscUserConsume;
import com.ruoyi.sxsc.consume.domain.SxscUserConsumeBuy;
import com.ruoyi.sxsc.consume.mapper.SxscUserConsumeMapper;
import com.ruoyi.sxsc.consume.service.*;
import com.ruoyi.sxsc.person.domain.*;
import com.ruoyi.sxsc.person.mapper.SxscUserInfoMapper;
import com.ruoyi.sxsc.person.service.*;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;


/**
 * 任务调度
 */
@Component("sxscTask")
public class TaskUtils {

    @Autowired
    ISxscUserConsumeBuyService iSxscUserConsumeBuyService;

    @Autowired
    ISysConfigService iSysConfigService;

    @Autowired
    ISxscBillIntegralService iSxscBillIntegralService;

    @Autowired
    ISxscUserConsumeService iSxscUserConsumeService;

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    private ISxscCommoditySpecificationsService iSxscCommoditySpecificationsService;

    @Autowired
    SxscCommodityOrderMapper sxscCommodityOrderMapper;


    /**
     *  抵押30天，抵押结束后，优惠券将会退还至账户中
     */
    public void mortgageToReturn(){
        LambdaQueryWrapper<SxscUserConsumeBuy> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(SxscUserConsumeBuy::getStatus,1);
        wrapper.apply("DATE(buy_start_time) < CURDATE() - INTERVAL (mortgage_days-1) DAY ");
        List<SxscUserConsumeBuy> userConsumeBuys= iSxscUserConsumeBuyService.list(wrapper);
        for(SxscUserConsumeBuy consumeBuy:userConsumeBuys){
            //优惠券更新状态
            LambdaUpdateWrapper<SxscUserConsume> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(SxscUserConsume::getConsumeNumber,consumeBuy.getConsumeNumber());
            updateWrapper.set(SxscUserConsume::getStatus, 1L);
            updateWrapper.set(SxscUserConsume::getUpdateTime,DateUtils.getNowDate());
            iSxscUserConsumeService.update(updateWrapper);
            consumeBuy.setBuyEndTime(DateUtils.getNowDate());
            consumeBuy.setStatus(0l);
            iSxscUserConsumeBuyService.updateSxscUserConsumeBuy(consumeBuy);
        }
    }


    @Autowired
    ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    SxscUserConsumeMapper sxscUserConsumeMapper;


    /**
     *  判断是否有效用户
     *  用户实名认证+质押100优惠券；
     */
    public void effectiveUser(){
        //查询已经实名且不是有效的用户
        LambdaQueryWrapper<SxscUserInfo> userWrapper=new LambdaQueryWrapper<>();
        userWrapper.isNotNull(SxscUserInfo::getIdentityTime);
        userWrapper.eq(SxscUserInfo::getEffective,0);
        userWrapper.eq(SxscUserInfo::getDelFlag,0);
        for(SxscUserInfo userInfo: iSxscUserInfoService.list(userWrapper)){
            BigDecimal consumeAmount=sxscUserConsumeMapper.sumConsumeAmount(2l,userInfo.getUserId());
            if(consumeAmount.compareTo(new BigDecimal("100"))>=0){
                 LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
                 updateWrapper.set(SxscUserInfo::getEffective, 1L);
                 updateWrapper.set(SxscUserInfo::getUpdateTime,DateUtils.getNowDate());
                 updateWrapper.set(SxscUserInfo::getUpdateBy,"系统");
                 updateWrapper.eq(SxscUserInfo::getUserId,userInfo.getUserId());
                 iSxscUserInfoService.update(updateWrapper);
                 String orderId=IdUtils.fastSimpleUUID();
                 //推荐人获得5个积分
                 if(StringUtils.isNotNull(userInfo.getParentId())){
                     iSxscBillIntegralService.insertSxscBillIntegral(orderId,IntegralBillConstants.Newcomer_Shopping,5l,new BigDecimal("5"),userInfo.getParentId());
                 }
             }
        }
    }

    /**
     *  取消商品订单
     */
    @Transactional
    public void cancelOrder(){
        // 计算半小时前的毫秒数（半小时 = 30分钟 * 60秒/分钟 * 1000毫秒/秒）
        String millis=iSysConfigService.selectConfigByKey("sxsc.commodity.order.cancel.millis");
        long halfHourMillis = Long.parseLong(millis) * 60 * 1000;
        // 获取当前时间的时间戳（毫秒）
        long currentTimeMillis = Instant.now().toEpochMilli();
        // 计算半小时前的时间戳（毫秒）
        long halfHourAgoMillis = currentTimeMillis - halfHourMillis;
        LambdaQueryWrapper<SxscCommodityOrder> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityOrder::getStatus,1l);
        queryWrapper.lt(SxscCommodityOrder::getCreateTime,new Date(halfHourAgoMillis));
        List<SxscCommodityOrder> list=iSxscCommodityOrderService.list(queryWrapper);
        for(SxscCommodityOrder order:list){
            LambdaUpdateWrapper<SxscCommodityOrder> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.set(SxscCommodityOrder::getStatus,7l);
            updateWrapper.set(SxscCommodityOrder::getUpdateTime,DateUtils.getNowDate());
            updateWrapper.set(SxscCommodityOrder::getUpdateBy,"系统任务");
            updateWrapper.eq(SxscCommodityOrder::getId,order.getId());
            iSxscCommodityOrderService.update(updateWrapper);
            iSxscCommoditySpecificationsService.updateSxscCommoditySpecifications(order.getSpecificationsId(),order.getNumber()*1);
        }
    }

    /**
     *  商品订单确认收货
     */
    @Transactional
    public void confirmOrder(){
        String day=iSysConfigService.selectConfigByKey("sxsc.commodity.order.confirm.day");
        long halfDay = Long.parseLong(day) * 86400000;
        // 获取当前时间的时间戳（毫秒）
        long currentTimeMillis = Instant.now().toEpochMilli();
        // 计算day天前的时间戳（毫秒）
        long halfDayAgo = currentTimeMillis - halfDay;
        LambdaQueryWrapper<SxscCommodityOrder> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityOrder::getStatus,3l);
        queryWrapper.eq(SxscCommodityOrder::getAfterSalesFlag,0l);
        queryWrapper.lt(SxscCommodityOrder::getDeliveryDateTime,new Date(halfDayAgo));
        List<SxscCommodityOrder> list=iSxscCommodityOrderService.list(queryWrapper);
        for(SxscCommodityOrder order:list){
            LambdaUpdateWrapper<SxscCommodityOrder> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(SxscCommodityOrder::getId,order.getId());
            updateWrapper.set(SxscCommodityOrder::getStatus,4l);
            updateWrapper.set(SxscCommodityOrder::getReceivingDateTime,DateUtils.getNowDate());
            updateWrapper.set(SxscCommodityOrder::getUpdateBy,"系统");
            iSxscCommodityOrderService.update(updateWrapper);
            //确认收货
            iSxscCommodityOrderService.confirmReceipt(order);
        }
    }

    @Autowired
    ISxscCommodityEvaluateService iSxscCommodityEvaluateService;

    /**
     *  商品订单自动好评
     */
    @Transactional
    public void evaluate(){
        String day=iSysConfigService.selectConfigByKey("sxsc.commodity.order.evaluate.day");
        long halfDay = Long.parseLong(day) * 86400000;
        // 获取当前时间的时间戳（毫秒）
        long currentTimeMillis = Instant.now().toEpochMilli();
        // 计算day天前的时间戳（毫秒）
        long halfDayAgo = currentTimeMillis - halfDay;
        LambdaQueryWrapper<SxscCommodityOrder> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodityOrder::getStatus,4l);
        queryWrapper.lt(SxscCommodityOrder::getReceivingDateTime,new Date(halfDayAgo));
        List<SxscCommodityOrder> list=iSxscCommodityOrderService.list(queryWrapper);
        for(SxscCommodityOrder order:list){
            SxscCommodityEvaluate evaluate =new SxscCommodityEvaluate();
            evaluate.setCommodityId(order.getCommodityId());
            evaluate.setCommodityOrderId(order.getId());
            evaluate.setUserId(order.getBuyerUserId());
            evaluate.setEnterpriseUserId(order.getSellerUserId());
            evaluate.setLogisticsStarLevel(BigDecimal.valueOf(5));
            evaluate.setCommodityStarLevel(BigDecimal.valueOf(5));
            evaluate.setDescribeStarLevel(BigDecimal.valueOf(5));
            evaluate.setSellerStarLevel(BigDecimal.valueOf(5));
            evaluate.setMessage("系统默认好评");
            iSxscCommodityEvaluateService.insertSxscCommodityEvaluateTask(evaluate);
        }
    }
    @Autowired
    ISxscBillSystemIntegralGiveService iSxscBIllSystemIntegralGiveService;
    /**
     *  系统按比例赠送用户积分
     */
    @Transactional
    public  void integralGive(){
        List<SxscBillSystemIntegralGive> list= iSxscBIllSystemIntegralGiveService.selectSxscUserIntegralGiveList(new SxscBillSystemIntegralGive());
        for(SxscBillSystemIntegralGive sxscBIllSystemIntegralGive :list){
            if(sxscBIllSystemIntegralGive.getProportion().compareTo(new BigDecimal("0"))<=0){
                continue;
            }
            if(sxscBIllSystemIntegralGive.getBalance().compareTo(new BigDecimal("0"))<=0){
                continue;
            }
            SxscUserInfo sxscUserInfoData=iSxscUserInfoService.getById(sxscBIllSystemIntegralGive.getUserId());
            if(StringUtils.isNull(sxscUserInfoData)){
                continue;
            }
            String orderId= IdUtils.fastSimpleUUID();
            BigDecimal integral= sxscBIllSystemIntegralGive.getBaseNumber().multiply(sxscBIllSystemIntegralGive.getProportion());
            iSxscBillIntegralService.insertSxscBillIntegral(orderId,"系统赠送",5l,integral, sxscBIllSystemIntegralGive.getUserId());
            sxscBIllSystemIntegralGive.setBalance(sxscBIllSystemIntegralGive.getBalance().subtract(integral));
            iSxscBIllSystemIntegralGiveService.updateSxscUserIntegralGive(sxscBIllSystemIntegralGive);
        }
    }
    @Autowired
    ISxscCommodityService iSxscCommodityService;
    /**
    * 商品定时排序功能
    *
    * */
    @Transactional
    public void commoditySortNumber(){
        String limit=iSysConfigService.selectConfigByKey("sxsc.commodity.sort.number.limit");
        LambdaQueryWrapper<SxscCommodity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodity::getStatus,2);
        queryWrapper.orderByAsc(SxscCommodity::getSortNumber);
        queryWrapper.last(" limit "+limit);
        List<SxscCommodity> list=iSxscCommodityService.list(queryWrapper);

        LambdaQueryWrapper<SxscCommodity> wrapper=new LambdaQueryWrapper<>();
        wrapper.eq(SxscCommodity::getStatus,2);
        wrapper.orderByDesc(SxscCommodity::getSortNumber);
        wrapper.last(" limit 1");
        SxscCommodity sxscCommodityData=iSxscCommodityService.getOne(wrapper);
        int i=1;
        for(SxscCommodity sxscCommodity:list){
            iSxscCommodityService.updateSxscCommoditySortNumber(sxscCommodity.getId(),sxscCommodityData.getSortNumber()+i);
            i++;
        }
    }

    @Autowired
    ISxscUserConsumeParentRewardService iSxscUserConsumeParentRewardService;
    /**
     * 优惠券抵押添加奖励明细
     * 此任务必须优惠券抵押结束退还之前执行
     * */
    @Transactional
    public void consumeParentReward(){
        //获取昨天抵押明细数据
        LambdaQueryWrapper<SxscUserConsumeBuy> wrapper=new LambdaQueryWrapper<>();
        wrapper.apply(" DATE(buy_start_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) ");
        List<SxscUserConsumeBuy> userConsumeBuys= iSxscUserConsumeBuyService.list(wrapper);
        for(SxscUserConsumeBuy consumeBuy:userConsumeBuys){
            iSxscUserConsumeParentRewardService.insertSxscUserConsumeParentReward(consumeBuy);
        }
    }
    /**
     * 权益值释放奖励明细
     *
     * */
    @Transactional
    public void consumeParentRewardRelease(){
        LambdaQueryWrapper<SxscUserInfo> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.gt(SxscUserInfo::getIntegralSj,0);
        List<SxscUserInfo> userInfos=iSxscUserInfoService.list(queryWrapper);
        for(SxscUserInfo sxscUserInfo:userInfos){
            String orderId=IdUtils.fastSimpleUUID();
            BigDecimal integrate=sxscUserInfo.getIntegralSj().multiply(new BigDecimal("0.01"));
            iSxscBillIntegralService.insertSxscBillIntegral(orderId,"权益值释放扣除",4l,integrate.multiply(new BigDecimal("-1")),sxscUserInfo.getUserId());
            iSxscBillIntegralService.insertSxscBillIntegral(orderId,"权益值释放积分",5l,integrate,sxscUserInfo.getUserId());
        }
    }
    @Autowired
    private SxscUserInfoMapper sxscUserInfoMapper;

    @Autowired
    private ISxscUserCommissionService iSxscUserCommissionService;

    /**
     * 积分每日释放
     * 从SxscUserInfo的integralTy字段按每个用户的个人释放比例释放到SxscUserCommission
     */
    @Transactional
    public void integralDailyRelease() {
        // 查询所有有积分且设置了释放比例的用户
        LambdaQueryWrapper<SxscUserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.gt(SxscUserInfo::getIntegralTy, BigDecimal.ZERO);
        queryWrapper.gt(SxscUserInfo::getIntegralReleaseRatio, BigDecimal.ZERO);
        queryWrapper.eq(SxscUserInfo::getDelFlag, 0);
        List<SxscUserInfo> userInfoList = iSxscUserInfoService.list(queryWrapper);
        if (userInfoList.isEmpty()) {
            return;
        }
        String orderId = IdUtils.fastSimpleUUID();
        for (SxscUserInfo userInfo : userInfoList) {
            // 获取用户的个人释放比例
            BigDecimal userReleaseRatio = userInfo.getIntegralReleaseRatio();
            // 计算释放金额
            BigDecimal releaseAmount = userInfo.getIntegralTy().multiply(userReleaseRatio);

            // 如果释放金额大于0，则进行释放
            if (releaseAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 扣减用户积分
                iSxscBillIntegralService.insertSxscBillIntegral(orderId,"积分释放",5L,releaseAmount.multiply(new BigDecimal("-1")),userInfo.getUserId());

                iSxscUserCommissionService.insertSxscUserCommission(
                    userInfo.getUserId(),
                    orderId,
                    releaseAmount,
                    "积分每日释放"
                );
            }
        }

    }

    /**
     * 更新合伙人身份
     *
     * 当节点以下（团队）总业绩达到10万时，升级为初级合伙人，初级合伙人可获得新增业绩（10万-50万之间）5%的积分奖励。
     *
     * 当节点以下（团队）总业绩达到50万时，升级为中级合伙人，中级合伙人可获得新增业绩（50万-100万之间）8%的积分奖励。
     *
     * 当节点以下（团队）总业绩达到100万时，升级为高级合伙人，高级合伙人可获得新增业绩（100万-500万之间）11%的积分奖励。
     *
     * 当节点以下（团队）总业绩达到500万时，升级为联创合伙人，联创合伙人可获得新增业绩（500万-1000万之间）14%的积分奖励。
     *
     * 当节点以下（团队）总业绩达到1000万时，升级为共识合伙人，共识合伙人可获得新增业绩（1000万以上）17%的积分奖励。
     * */
    @Transactional
    public void updatePrimary(){
        LambdaQueryWrapper<SxscUserInfo> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscUserInfo::getDelFlag,0);
        queryWrapper.eq(SxscUserInfo::getCopartner,0);
        queryWrapper.isNotNull(SxscUserInfo::getIdentityTime);
        List<SxscUserInfo> userInfos=iSxscUserInfoService.list(queryWrapper);
        for(SxscUserInfo sxscUserInfo:userInfos){
            BigDecimal total=new  BigDecimal("0");
            List<SxscUserInfo> userInfoNodes= sxscUserInfoMapper.nodeAccByUserId(sxscUserInfo.getUserId(),null);
            for(SxscUserInfo nodesUser:userInfoNodes){
                total=sxscCommodityOrderMapper.totalByUserId(nodesUser.getUserId()).add(total);
            }
            if(total.compareTo(new BigDecimal("100000"))>=0){
                LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
                updateWrapper.set(SxscUserInfo::getCopartner,1L);
                updateWrapper.eq(SxscUserInfo::getUserId,sxscUserInfo.getUserId());
                iSxscUserInfoService.update(updateWrapper);
            }else if(total.compareTo(new BigDecimal("500000"))>=0){
                LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
                updateWrapper.set(SxscUserInfo::getCopartner,2L);
                updateWrapper.eq(SxscUserInfo::getUserId,sxscUserInfo.getUserId());
                iSxscUserInfoService.update(updateWrapper);
            }else if(total.compareTo(new BigDecimal("1000000"))>=0){
                LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
                updateWrapper.set(SxscUserInfo::getCopartner,3L);
                updateWrapper.eq(SxscUserInfo::getUserId,sxscUserInfo.getUserId());
                iSxscUserInfoService.update(updateWrapper);
            }else if(total.compareTo(new BigDecimal("5000000"))>=0){
                LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
                updateWrapper.set(SxscUserInfo::getCopartner,4L);
                updateWrapper.eq(SxscUserInfo::getUserId,sxscUserInfo.getUserId());
                iSxscUserInfoService.update(updateWrapper);
            }else if(total.compareTo(new BigDecimal("10000000"))>=0){
                LambdaUpdateWrapper<SxscUserInfo> updateWrapper=new LambdaUpdateWrapper<>();
                updateWrapper.set(SxscUserInfo::getCopartner,5L);
                updateWrapper.eq(SxscUserInfo::getUserId,sxscUserInfo.getUserId());
                iSxscUserInfoService.update(updateWrapper);
            }
        }
    }
}
