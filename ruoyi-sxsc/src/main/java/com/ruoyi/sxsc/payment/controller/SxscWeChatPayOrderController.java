package com.ruoyi.sxsc.payment.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.domain.SxscWeChatPayOrder;
import com.ruoyi.sxsc.payment.service.ISxscWeChatPayOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 微信支付订单信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
@RestController
@RequestMapping("/wechat/payment/order")
public class SxscWeChatPayOrderController extends BaseController
{
    @Autowired
    private ISxscWeChatPayOrderService sxscWeChatPayOrderService;

    /**
     * 查询微信支付订单信息列表
     */
    @PreAuthorize("@ss.hasPermi('wechat:payment:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscWeChatPayOrder sxscWeChatPayOrder)
    {
        startPage();
        List<SxscWeChatPayOrder> list = sxscWeChatPayOrderService.selectSxscWeChatPayOrderList(sxscWeChatPayOrder);
        return getDataTable(list);
    }

    /**
     * 获取微信支付订单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('wechat:payment:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscWeChatPayOrderService.selectSxscWeChatPayOrderById(id));
    }

    /**
     * 微信支付成功回调接口
     */
    @PostMapping(value="/wechat-notify")
    public String notify(HttpServletRequest request, HttpServletResponse response) {
        return sxscWeChatPayOrderService.notifyData(request, response);
    }

    /**
     * 微信支付成功线下回调
     */
    @PostMapping(value="/ali-trends/offline/{id}")
    @PreAuthorize("@ss.hasPermi('ali:trends:notify:offline')")
    @Log(title = "支付宝支付成功线下回调", businessType = BusinessType.UPDATE)
    public AjaxResult trendsNotifyOffline(@PathVariable("id") String id) {

        SxscWeChatPayOrder payment=sxscWeChatPayOrderService.selectSxscWeChatPayOrderById(id);

        //sxscWeChatPayOrderService.call("TRADE_SUCCESS",id,payment.getTotalAmount().toString(),"手动回调");
        return AjaxResult.success();
    }
}
