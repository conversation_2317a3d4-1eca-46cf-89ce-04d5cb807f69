package com.ruoyi.sxsc.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.payment.domain.SxscWeChatPayOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 微信支付订单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
public interface SxscWeChatPayOrderMapper extends BaseMapper<SxscWeChatPayOrder>
{

    /**
     * 查询微信支付订单支付金额
     * @param month 月份
     * @param userId 用户主键
     * @return 微信支付订单信息
     */
    @Select("<script>"+
            "select IFNULL(sum(actual_total_amount), 0) from sxsc_wechat_pay_order " +
            "where pay_status=1" +
            "<if test=' month!=null and month!=\"\" '>  and DATE_FORMAT(create_time,'%Y-%m') =#{month}   </if>"+
            "<if test=' userId!=null  '>  and buyer_user_id =#{userId}   </if>"+
            "</script>")
    BigDecimal weChatPaySum(@Param("month")String month, @Param("userId")Long userId);

}
