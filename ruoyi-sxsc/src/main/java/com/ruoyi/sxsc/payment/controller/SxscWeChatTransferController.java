package com.ruoyi.sxsc.payment.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.sxsc.payment.domain.SxscWeChatTransfer;
import com.ruoyi.sxsc.payment.service.ISxscWeChatTransferService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 微信转账信息Controller
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
@RestController
@RequestMapping("/wechat/payment/transfer")
public class SxscWeChatTransferController extends BaseController
{
    @Autowired
    private ISxscWeChatTransferService sxscWeChatTransferService;

    /**
     * 查询微信转账信息列表
     */
    @PreAuthorize("@ss.hasPermi('wechat:payment:transfer:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscWeChatTransfer sxscWeChatTransfer)
    {
        startPage();
        List<SxscWeChatTransfer> list = sxscWeChatTransferService.selectSxscWeChatTransferList(sxscWeChatTransfer);
        return getDataTable(list);
    }

    /**
     * 获取微信转账信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('wechat:payment:transfer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(sxscWeChatTransferService.selectSxscWeChatTransferById(id));
    }
    /**
     * 重新发起转账
     */
    @PreAuthorize("@ss.hasPermi('wechat:payment:transfer:reissue')")
    @Log(title = "微信转账", businessType = BusinessType.UPDATE)
    @PutMapping("/reissue/{id}")
    public AjaxResult reissueTransfer(@PathVariable String id)
    {
        return sxscWeChatTransferService.reissueTransfer(id);
    }


}
