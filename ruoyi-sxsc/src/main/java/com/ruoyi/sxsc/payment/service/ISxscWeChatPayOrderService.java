package com.ruoyi.sxsc.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sxsc.payment.domain.SxscWeChatPayOrder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 微信支付订单信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
public interface ISxscWeChatPayOrderService extends IService<SxscWeChatPayOrder>
{
    /**
     * 查询微信支付订单信息
     * 
     * @param id 微信支付订单信息主键
     * @return 微信支付订单信息
     */
    SxscWeChatPayOrder selectSxscWeChatPayOrderById(String id);

    /**
     * 查询微信支付订单支付总金额
     * @param month 月份
     * @param userId 用户主键
     * @return 微信支付订单信息
     */
    BigDecimal weChatPaySum(String month, Long userId);

    /**
     * 查询微信支付订单信息列表
     * 
     * @param sxscWeChatPayOrder 微信支付订单信息
     * @return 微信支付订单信息集合
     */
    List<SxscWeChatPayOrder> selectSxscWeChatPayOrderList(SxscWeChatPayOrder sxscWeChatPayOrder);

    /**
     * 新增微信支付订单信息
     * 
     * @param sxscWeChatPayOrder 微信支付订单信息
     * @return 结果
     */
    String insertSxscWeChatPayOrder(SxscWeChatPayOrder sxscWeChatPayOrder);

    /**
     * 修改微信支付订单信息
     * 
     * @param sxscWeChatPayOrder 微信支付订单信息
     * @return 结果
     */
    int updateSxscWeChatPayOrder(SxscWeChatPayOrder sxscWeChatPayOrder);

    /**
     * 批量删除微信支付订单信息
     * 
     * @param ids 需要删除的微信支付订单信息主键集合
     * @return 结果
     */
    int deleteSxscWeChatPayOrderByIds(String[] ids);

    /**
     * 删除微信支付订单信息信息
     * 
     * @param id 微信支付订单信息主键
     * @return 结果
     */
    int deleteSxscWeChatPayOrderById(String id);

    /**
     * 微信支付成功回调处理
     * 
     * @param request 请求
     * @param response 响应
     * @return 结果
     */
    String notifyData(HttpServletRequest request, HttpServletResponse response);

    /**
     * 查询订单状态
     * 
     * @param outTradeNo 商户订单号
     * @return 订单状态
     */
    String queryOrderStatus(String outTradeNo);

    /**
     * 关闭订单
     * 
     * @param outTradeNo 商户订单号
     * @return 结果
     */
    boolean closeOrder(String outTradeNo);
}
