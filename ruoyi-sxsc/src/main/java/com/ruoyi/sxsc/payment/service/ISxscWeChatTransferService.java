package com.ruoyi.sxsc.payment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.payment.domain.SxscWeChatTransfer;

import java.math.BigDecimal;
import java.util.List;

/**
 * 微信转账信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
public interface ISxscWeChatTransferService extends IService<SxscWeChatTransfer>
{
    /**
     * 查询微信转账信息
     * 
     * @param id 微信转账信息主键
     * @return 微信转账信息
     */
    SxscWeChatTransfer selectSxscWeChatTransferById(String id);

    /**
     * 查询微信转账总金额
     *
     * @param transferRemark 转账备注
     * @param month 月份
     * @return 微信转账信息
     */
    BigDecimal weChatTransferSum(String transferRemark, String month);

    /**
     * 查询微信转账信息列表
     * 
     * @param sxscWeChatTransfer 微信转账信息
     * @return 微信转账信息集合
     */
    List<SxscWeChatTransfer> selectSxscWeChatTransferList(SxscWeChatTransfer sxscWeChatTransfer);

    /**
     * 新增微信转账信息
     * 
     * @param amount 金额
     * @param userId 用户主键
     * @param orderId 订单主键
     * @param transferRemark 转账备注
     * @param openid 用户openid
     * @param userName 用户真实姓名
     * @return 结果
     */
    SxscWeChatTransfer insertSxscWeChatTransfer(BigDecimal amount, Long userId, String orderId, 
                                               String transferRemark, String openid, String userName);

    /**
     * 修改微信转账信息
     * 
     * @param sxscWeChatTransfer 微信转账信息
     * @return 结果
     */
    int updateSxscWeChatTransfer(SxscWeChatTransfer sxscWeChatTransfer);


    /**
     * 重新发起转账
     * 
     * @param id 主键
     * @return 结果
     */
    AjaxResult reissueTransfer(String id);

    /**
     * 更新转账状态
     * 
     * @param id 主键
     * @return 结果
     */
    AjaxResult updateTransferStatus(String id);
}
