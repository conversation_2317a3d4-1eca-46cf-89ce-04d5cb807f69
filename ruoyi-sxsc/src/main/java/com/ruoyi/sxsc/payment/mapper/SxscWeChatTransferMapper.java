package com.ruoyi.sxsc.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.sxsc.payment.domain.SxscWeChatTransfer;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 微信转账信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
public interface SxscWeChatTransferMapper extends BaseMapper<SxscWeChatTransfer>
{

    /**
     * 查询微信转账总金额
     * @param month 月份
     * @param transferRemark 转账备注
     * @return 微信转账总金额
     */
    @Select("<script>"+
            "select IFNULL(sum(amount), 0) from sxsc_wechat_transfer " +
            "where status=1" +
            "<if test=' month!=null and month!=\"\" '>  and DATE_FORMAT(create_time,'%Y-%m') =#{month}   </if>"+
            "<if test=' transferRemark!=null and transferRemark!=\"\" '>  and transfer_remark =#{transferRemark}   </if>"+
            "</script>")
    BigDecimal weChatTransferSum(@Param("transferRemark")String transferRemark, @Param("month")String month);

}
