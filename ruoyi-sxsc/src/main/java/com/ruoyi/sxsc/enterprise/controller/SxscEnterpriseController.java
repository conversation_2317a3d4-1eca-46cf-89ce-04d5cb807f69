package com.ruoyi.sxsc.enterprise.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;
import com.ruoyi.sxsc.enterprise.service.ISxscEnterpriseService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业信息
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@RestController
@RequestMapping("/enterprise/enterprise")
public class SxscEnterpriseController extends BaseController
{
    @Autowired
    private ISxscEnterpriseService sxscEnterpriseService;

    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    private ISysConfigService iSysConfigService;

    /**
     * 查询企业信息列表
     */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscEnterprise sxscEnterprise)
    {
        startPage();
        List<SxscEnterprise> list = sxscEnterpriseService.selectSxscEnterpriseList(sxscEnterprise);
        return getDataTable(list);
    }

    /**
     * 获取商家信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:query')")
    @GetMapping(value = "/detail")
    public AjaxResult detail(@RequestParam(required = false) Long userId)
    {
        if(StringUtils.isNull(userId)){
            userId=SecurityUtils.getUserId();
        }
        return success(sxscEnterpriseService.selectEnterpriseByUserId(userId));
    }

    /**
     * 获取企业信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscEnterpriseService.selectSxscEnterpriseById(id));
    }

    /**
     * 新增企业信息
     */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:add')")
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscEnterprise sxscEnterprise)
    {
        return sxscEnterpriseService.insertSxscEnterprise(sxscEnterprise);
    }

    /**
     * 修改企业信息
     */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscEnterprise sxscEnterprise)
    {
        return sxscEnterpriseService.updateSxscEnterprise(sxscEnterprise);
    }


    /**
     * 修改企业是否自营
     */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping("/self")
    public AjaxResult self(@RequestBody SxscEnterprise sxscEnterprise)
    {
        return sxscEnterpriseService.updateEnterpriseSelf(sxscEnterprise.getSelf(),sxscEnterprise.getId());
    }


    /**
     * 根据经纬度获取附近商家
     * @param  longitude 经度
     * @param  latitude 纬度
     * */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:list')")
    @GetMapping(value="/nearby")
    public AjaxResult nearby(@RequestParam(value = "longitude")String longitude,
                             @RequestParam(value = "latitude")String latitude) {

        return sxscEnterpriseService.nearby(longitude,latitude);
    }

    /**
     * 删除企业信息
     */
    @PreAuthorize("@ss.hasPermi('enterprise:enterprise:remove')")
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sxscEnterpriseService.deleteSxscEnterpriseByIds(ids));
    }

    /**
     * 获取商家静态收款码
     * */
    @GetMapping(value="/staticCode")
    public AjaxResult staticCode() {
        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("02")){
            return error("请先填申请入驻商家");
        }
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.getById(SecurityUtils.getUserId());
        if(StringUtils.isEmpty(sxscUserInfo.getAliPayName())){
            return error("请先填写收款人支付宝账号！");
        }
        if(StringUtils.isEmpty(sxscUserInfo.getAliPayAcc())){
            return error("请先填写收款人支付宝账号！");
        }
        if(StringUtils.isEmpty(sxscUserInfo.getIdentityNumber())){
            return error("请先进行实名认证");
        }
//        if(StringUtils.isEmpty(sxscUserInfo.getWechatOpenId())){
//            return error("请先绑定微信账号");
//        }
        return success("/pages/scan/Payment?businessPersonnelId="+SecurityUtils.getUserId()+"-"+ Md5Utils.hash(sxscUserInfo.getIdentityNumber()));
    }
}
