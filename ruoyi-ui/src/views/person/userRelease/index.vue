<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户昵称" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="请输入用户昵称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="userList" @selection-change="handleSelectionChange">
      <el-table-column label="用户ID" align="center" prop="userId" width="80" />
      <el-table-column label="用户昵称" align="center" prop="sysUser.nickName" />
      <el-table-column label="手机号码" align="center" prop="sysUser.phonenumber" />
      <el-table-column label="当前积分" align="center" prop="integralTy" width="120">
        <template slot-scope="scope">
          <span style="color: #E6A23C;">{{ scope.row.integralTy || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="释放比例" align="center" prop="integralReleaseRatio" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.integralReleaseRatio > 0" type="success">
            {{ (scope.row.integralReleaseRatio * 100).toFixed(2) }}%
          </el-tag>
          <el-tag v-else type="info">未设置</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="预计每日释放" align="center" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.integralTy > 0 && scope.row.integralReleaseRatio > 0" style="color: #67C23A;">
            {{ (scope.row.integralTy * scope.row.integralReleaseRatio).toFixed(4) }}
          </span>
          <span v-else style="color: #909399;">0</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['person:info:integral:userRelease']">设置比例</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 设置释放比例对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="用户信息">
          <div style="color: #606266;">
            <p><strong>昵称：</strong>{{ form.sysUser ? form.sysUser.nickName : '' }}</p>
            <p><strong>手机号：</strong>{{ form.sysUser ? form.sysUser.phonenumber : '' }}</p>
            <p><strong>当前积分：</strong>{{ form.integralTy || 0 }}</p>
          </div>
        </el-form-item>
        <el-form-item label="释放比例" prop="integralReleaseRatio">
          <el-input-number v-model="form.integralReleaseRatio" :precision="4" :step="0.0001" :min="0" :max="1"
            placeholder="请输入释放比例" style="width: 200px" />
          <span style="margin-left: 10px; color: #909399;">
            范围：0.0000 - 1.0000
          </span>
        </el-form-item>
        <el-form-item label="预计每日释放">
          <span style="color: #67C23A; font-weight: bold;">
            {{ ((form.integralTy || 0) * (form.integralReleaseRatio || 0)).toFixed(4) }}
          </span>
        </el-form-item>
        <el-form-item label="说明">
          <div style="color: #606266; font-size: 12px; line-height: 1.5;">
            <p>• 释放比例支持小数点后四位精度</p>
            <p>• 例如：0.0100 表示每日释放1%的积分</p>
            <p>• 设置为0.0000表示暂停该用户的积分释放</p>
            <p>• 释放的积分将转换为等额佣金</p>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, updateUserRelease } from "@/api/person/info";

export default {
  name: "UserIntegralRelease",
  dicts: ['sys_user_type', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        nickName: null,
        phonenumber: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        integralReleaseRatio: [
          { required: true, message: "释放比例不能为空", trigger: "blur" },
          { type: "number", min: 0, max: 1, message: "释放比例必须在0-1之间", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.userList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: null,
        integralTy: null,
        integralReleaseRatio: 0.0000,
        sysUser: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.userId || this.ids[0];
      const userInfo = row || this.userList.find(item => item.userId === userId);

      this.form = {
        userId: userInfo.userId,
        integralTy: userInfo.integralTy || 0,
        integralReleaseRatio: userInfo.integralReleaseRatio || 0.0000,
        sysUser: userInfo.sysUser
      };

      this.open = true;
      this.title = "设置用户积分释放比例";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const updateData = {
            userId: this.form.userId,
            integralReleaseRatio: this.form.integralReleaseRatio
          };

          updateUserRelease(updateData).then(response => {
            this.$modal.msgSuccess("设置成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('person/info/export', {
        ...this.queryParams
      }, `user_integral_release_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
