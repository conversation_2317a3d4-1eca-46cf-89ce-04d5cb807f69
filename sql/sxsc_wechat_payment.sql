-- ----------------------------
-- 微信支付订单表
-- ----------------------------
DROP TABLE IF EXISTS `sxsc_wechat_pay_order`;
CREATE TABLE `sxsc_wechat_pay_order` (
  `id` varchar(64) NOT NULL COMMENT '订单主键',
  `subject` varchar(256) DEFAULT NULL COMMENT '订单名称',
  `total_amount` decimal(10,2) DEFAULT NULL COMMENT '付款金额(分)',
  `actual_total_amount` decimal(10,2) DEFAULT NULL COMMENT '实际支付金额(分)',
  `seller_user_id` bigint(20) DEFAULT NULL COMMENT '卖家ID',
  `buyer_user_id` bigint(20) DEFAULT NULL COMMENT '买家ID',
  `pay_status` bigint(20) DEFAULT '0' COMMENT '0待支付1支付成功2交易到账',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付订单号',
  `prepay_id` varchar(64) DEFAULT NULL COMMENT '预支付交易会话标识',
  `response` text COMMENT '调用微信支付返回信息',
  `notify_date` datetime DEFAULT NULL COMMENT '支付成功回调时间',
  `notify_response` text COMMENT '微信支付回调返回信息',
  `order_type` bigint(20) DEFAULT NULL COMMENT '订单类型 1商品2静态码3优惠券4商品保证金',
  `trade_type` varchar(32) DEFAULT 'APP' COMMENT '支付方式 JSAPI-公众号支付 NATIVE-扫码支付 APP-APP支付 MWEB-H5支付',
  `openid` varchar(128) DEFAULT NULL COMMENT '用户标识(openid)',
  `description` varchar(256) DEFAULT NULL COMMENT '商品描述',
  `attach` varchar(256) DEFAULT NULL COMMENT '附加数据',
  `out_trade_no` varchar(64) DEFAULT NULL COMMENT '商户订单号',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_buyer_user_id` (`buyer_user_id`),
  KEY `idx_seller_user_id` (`seller_user_id`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_out_trade_no` (`out_trade_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付订单信息表';

-- ----------------------------
-- 微信转账记录表
-- ----------------------------
DROP TABLE IF EXISTS `sxsc_wechat_transfer`;
CREATE TABLE `sxsc_wechat_transfer` (
  `id` varchar(64) NOT NULL COMMENT '主键',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单号',
  `out_batch_no` varchar(64) DEFAULT NULL COMMENT '商户转账单号',
  `batch_id` varchar(64) DEFAULT NULL COMMENT '微信转账单号',
  `openid` varchar(128) DEFAULT NULL COMMENT '收款方openid',
  `user_name` varchar(64) DEFAULT NULL COMMENT '收款方真实姓名',
  `amount` decimal(10,2) DEFAULT NULL COMMENT '转账金额(分)',
  `transfer_remark` varchar(256) DEFAULT NULL COMMENT '转账备注',
  `transfer_scene` varchar(32) DEFAULT '1000' COMMENT '转账场景',
  `detail_id` varchar(64) DEFAULT NULL COMMENT '转账详情单号',
  `transfer_status` varchar(32) DEFAULT 'PROCESSING' COMMENT '转账状态 PROCESSING-转账中 SUCCESS-转账成功 FAILED-转账失败',
  `fail_reason` varchar(256) DEFAULT NULL COMMENT '失败原因',
  `response` text COMMENT '返回信息',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户主键',
  `status` bigint(20) DEFAULT '2' COMMENT '状态1成功0失败2处理中',
  `transfer_time` datetime DEFAULT NULL COMMENT '转账完成时间',
  `notify_response` text COMMENT '微信回调返回信息',
  `notify_time` datetime DEFAULT NULL COMMENT '回调时间',
  `transfer_type` bigint(20) DEFAULT '1' COMMENT '转账类型 1-企业付款到零钱 2-企业付款到银行卡',
  `bank_card_no` varchar(32) DEFAULT NULL COMMENT '银行卡号(转账到银行卡时使用)',
  `bank_code` varchar(32) DEFAULT NULL COMMENT '银行编码(转账到银行卡时使用)',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '银行名称(转账到银行卡时使用)',
  `province` varchar(32) DEFAULT NULL COMMENT '省份(转账到银行卡时使用)',
  `city` varchar(32) DEFAULT NULL COMMENT '城市(转账到银行卡时使用)',
  `mobile` varchar(16) DEFAULT NULL COMMENT '手机号(转账到银行卡时使用)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_transfer_status` (`transfer_status`),
  KEY `idx_out_batch_no` (`out_batch_no`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信转账记录表';

-- ----------------------------
-- 插入菜单权限数据
-- ----------------------------
-- 微信支付管理菜单
INSERT INTO `sys_menu` VALUES (2100, '微信支付管理', 0, 5, 'wechatpay', NULL, NULL, 1, 0, 'M', '0', '0', '', 'money', 'admin', '2024-08-02 10:00:00', '', NULL, '微信支付管理目录');

-- 微信支付订单菜单
INSERT INTO `sys_menu` VALUES (2101, '微信支付订单', 2100, 1, 'order', 'wechat/payment/order/index', NULL, 1, 0, 'C', '0', '0', 'wechat:payment:order:list', 'shopping', 'admin', '2024-08-02 10:00:00', '', NULL, '微信支付订单菜单');
INSERT INTO `sys_menu` VALUES (2102, '微信支付订单查询', 2101, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:order:query', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2103, '微信支付订单新增', 2101, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:order:add', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2104, '微信支付订单修改', 2101, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:order:edit', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2105, '微信支付订单删除', 2101, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:order:remove', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2106, '微信支付订单关闭', 2101, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:order:close', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');

-- 微信转账菜单
INSERT INTO `sys_menu` VALUES (2107, '微信转账', 2100, 2, 'transfer', 'wechat/payment/transfer/index', NULL, 1, 0, 'C', '0', '0', 'wechat:payment:transfer:list', 'money', 'admin', '2024-08-02 10:00:00', '', NULL, '微信转账菜单');
INSERT INTO `sys_menu` VALUES (2108, '微信转账查询', 2107, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:transfer:query', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2109, '微信转账修改', 2107, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:transfer:edit', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2110, '微信转账删除', 2107, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:transfer:remove', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2111, '微信转账重新发起', 2107, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:transfer:reissue', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2112, '微信转账状态更新', 2107, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'wechat:payment:transfer:status', '#', 'admin', '2024-08-02 10:00:00', '', NULL, '');

-- ----------------------------
-- 插入字典数据
-- ----------------------------
-- 微信支付状态字典
INSERT INTO `sys_dict_type` VALUES (100, '微信支付状态', 'sxsc_wechat_pay_status', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '微信支付订单状态列表');
INSERT INTO `sys_dict_data` VALUES (100, 1, '待支付', '0', 'sxsc_wechat_pay_status', '', 'warning', 'N', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '待支付状态');
INSERT INTO `sys_dict_data` VALUES (101, 2, '支付成功', '1', 'sxsc_wechat_pay_status', '', 'success', 'N', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '支付成功状态');
INSERT INTO `sys_dict_data` VALUES (102, 3, '交易到账', '2', 'sxsc_wechat_pay_status', '', 'info', 'N', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '交易到账状态');

-- 微信转账状态字典
INSERT INTO `sys_dict_type` VALUES (101, '微信转账状态', 'sxsc_wechat_transfer_status', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '微信转账状态列表');
INSERT INTO `sys_dict_data` VALUES (103, 1, '失败', '0', 'sxsc_wechat_transfer_status', '', 'danger', 'N', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '转账失败状态');
INSERT INTO `sys_dict_data` VALUES (104, 2, '成功', '1', 'sxsc_wechat_transfer_status', '', 'success', 'N', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '转账成功状态');
INSERT INTO `sys_dict_data` VALUES (105, 3, '处理中', '2', 'sxsc_wechat_transfer_status', '', 'warning', 'N', '0', 'admin', '2024-08-02 10:00:00', '', NULL, '转账处理中状态');
