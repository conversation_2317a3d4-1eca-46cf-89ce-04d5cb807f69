# 微信支付和转账功能说明

## 概述

本次更新为系统添加了完整的微信支付和转账功能，包括前端页面、API接口和后端服务。功能参考了现有的支付宝支付和转账模块的设计。

## 新增功能

### 1. 微信支付订单管理
- **页面路径**: `ruoyi-ui/src/views/wechat/payment/order/index.vue`
- **API文件**: `ruoyi-ui/src/api/wechat/order.js`
- **功能特性**:
  - 微信支付订单列表查询
  - 支付状态筛选
  - 订单详情查看
  - 订单状态查询
  - 订单关闭功能
  - 数据导出

### 2. 微信转账管理
- **页面路径**: `ruoyi-ui/src/views/wechat/payment/transfer/index.vue`
- **API文件**: `ruoyi-ui/src/api/wechat/transfer.js`
- **功能特性**:
  - 微信转账记录列表查询
  - 转账状态筛选
  - 转账详情查看
  - 失败转账重新发起
  - 数据导出

### 3. 支付看板增强
- **页面路径**: `ruoyi-ui/src/views/payment/board/index.vue`
- **新增特性**:
  - 支付宝/微信支付切换标签页
  - 微信支付统计数据展示
  - 微信转账统计数据展示

## 文件结构

```
ruoyi-ui/src/
├── api/wechat/
│   ├── order.js          # 微信支付订单API
│   └── transfer.js       # 微信转账API
└── views/wechat/payment/
    ├── order/
    │   └── index.vue     # 微信支付订单页面
    └── transfer/
        └── index.vue     # 微信转账页面
```

## 后端支持

后端已经提供了完整的微信支付和转账功能支持：

### 控制器
- `SxscWeChatPayOrderController` - 微信支付订单控制器
- `SxscWeChatTransferController` - 微信转账控制器

### 服务类
- `SxscWeChatPayOrderServiceImpl` - 微信支付订单服务
- `SxscWeChatTransferServiceImpl` - 微信转账服务

### 工具类
- `WeChatPayUtils` - 微信支付工具类
- `WeChatTransferUtils` - 微信转账工具类

### 配置类
- `WeChatPayConfig` - 微信支付配置

## 数据库配置

执行以下SQL脚本来添加菜单权限：

```sql
-- 执行 sql/sxsc_wechat_payment.sql 文件
```

该脚本包含：
- 微信支付管理菜单
- 微信支付订单菜单及权限
- 微信转账菜单及权限
- 相关字典数据

## 菜单权限

### 微信支付订单权限
- `wechat:payment:order:list` - 查询列表
- `wechat:payment:order:query` - 查询详情
- `wechat:payment:order:add` - 新增订单
- `wechat:payment:order:edit` - 修改订单
- `wechat:payment:order:remove` - 删除订单
- `wechat:payment:order:close` - 关闭订单

### 微信转账权限
- `wechat:payment:transfer:list` - 查询列表
- `wechat:payment:transfer:query` - 查询详情
- `wechat:payment:transfer:edit` - 修改转账
- `wechat:payment:transfer:remove` - 删除转账
- `wechat:payment:transfer:reissue` - 重新发起转账

## 使用说明

### 1. 部署步骤
1. 执行SQL脚本添加菜单权限
2. 重启后端服务
3. 刷新前端页面
4. 在系统管理-菜单管理中分配相应权限给角色

### 2. 功能访问
- 微信支付订单：菜单路径 `微信支付管理 > 微信支付订单`
- 微信转账：菜单路径 `微信支付管理 > 微信转账`
- 支付看板：在原有支付看板页面中切换到"微信支付"标签页

### 3. 配置要求
确保在 `application.yml` 中正确配置了微信支付相关参数：

```yaml
wechat:
  pay:
    appId: "your_app_id"
    mchId: "your_merchant_id"
    privateKey: "your_private_key"
    merchantSerialNumber: "your_serial_number"
    apiV3Key: "your_api_v3_key"
    notifyUrl: "your_notify_url"
    # ... 其他配置
```

## 注意事项

1. 微信支付功能需要正确配置微信商户号和相关证书
2. 转账功能需要开通微信企业付款到零钱权限
3. 所有金额单位在数据库中以分为单位存储
4. 页面中的权限控制需要在角色管理中正确分配

## 技术特点

1. **代码复用**: 参考支付宝模块的设计模式，保持代码风格一致
2. **权限控制**: 完整的菜单权限和按钮权限控制
3. **数据安全**: 敏感信息加密存储和传输
4. **用户体验**: 友好的操作界面和错误提示
5. **扩展性**: 预留了扩展接口，便于后续功能增强
